                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\ai_apps\loan_app_flutter\build\app\intermediates\cxx\RelWithDebInfo\192j516p\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\ai_apps\loan_app_flutter\build\app\intermediates\cxx\RelWithDebInfo\192j516p\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\ai_apps\loan_app_flutter\android\app\.cxx\RelWithDebInfo\192j516p\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2