import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:world_bank_loan/core/api/api_endpoints.dart';
import '../auth/saved_login/user_session.dart';

class WithdrawRequestProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;

  Future<void> submitWithdrawRequest({
    required String amount,
    required String pin,
    required BuildContext context,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();

    String? token = await UserSession.getToken();
    if (token == null) {
      _errorMessage = 'Token not found. Please login again.';
      _isLoading = false;
      notifyListeners();
      return;
    }
    if (amount.isEmpty || pin.isEmpty) {
      _errorMessage = 'Amount and Pin cannot be empty';
      _isLoading = false;
      notifyListeners();
      return;
    }
    try {
      final response = await http.post(
        Uri.parse(ApiEndpoints.withdraw),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: {
          'amount': amount,
          'pin': pin,
        },
      );
      if (response.statusCode == 201) {
        _successMessage = 'Withdraw Request Submitted';
      } else {
        _errorMessage = 'Failed to submit request. please try again later!';
      }
    } catch (error) {
      _errorMessage = 'Something went wrong. please try again later!';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }
}
