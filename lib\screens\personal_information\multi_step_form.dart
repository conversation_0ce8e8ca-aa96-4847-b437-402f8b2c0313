import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:world_bank_loan/core/theme/app_theme.dart';
import 'dart:io';
import 'package:world_bank_loan/providers/personal_info_provider.dart';
import 'package:world_bank_loan/screens/loan_apply_screen/loan_apply_screen.dart';
import 'package:world_bank_loan/core/api/api_service.dart';

import 'steps/personal_info_step.dart';
import 'steps/nominee_info_step.dart';
import 'steps/id_verification_step.dart';
import 'steps/bank_account_step.dart';
import 'package:flutter/foundation.dart';

class MultiStepPersonalInfoForm extends StatefulWidget {
  const MultiStepPersonalInfoForm({super.key});

  @override
  _MultiStepPersonalInfoFormState createState() =>
      _MultiStepPersonalInfoFormState();
}

class _MultiStepPersonalInfoFormState extends State<MultiStepPersonalInfoForm> {
  final GlobalKey<FormState> _personalInfoFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _nomineeInfoFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _idVerificationFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _bankAccountFormKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // The provider is already initialized in PersonalInfoScreen
    // No need to reinitialize here to avoid concurrent initializations
  }

  bool validateCurrentStep(PersonalInfoProvider provider) {
    switch (provider.currentStep) {
      case PersonalInfoStep.personalInfo:
        if (_personalInfoFormKey.currentState?.validate() ?? false) {
          return provider.validatePersonalInfo();
        }
        return false;

      case PersonalInfoStep.nomineeInfo:
        if (_nomineeInfoFormKey.currentState?.validate() ?? false) {
          return provider.validateNomineeInfo();
        }
        return false;

      case PersonalInfoStep.idVerification:
        if (_idVerificationFormKey.currentState?.validate() ?? false) {
          return provider.validateIdVerification();
        }
        return false;

      case PersonalInfoStep.bankAccount:
        if (_bankAccountFormKey.currentState?.validate() ?? false) {
          return provider.validateBankAccount();
        }
        return false;
    }
  }

  void showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PersonalInfoProvider>(
      builder: (context, provider, _) {
        final content = Column(
          children: [
            _buildProgressBar(provider),
            Expanded(
              child: _buildCurrentStep(provider),
            ),
            _buildNavigationButtons(provider),
          ],
        );

        final screenWidth = MediaQuery.of(context).size.width;
        // A common breakpoint for "desktop-like" layout.
        // If screen width is greater than 720, consider it desktop-like.
        final bool isDesktopLayout = screenWidth > 720;

        return Scaffold(
          body: isDesktopLayout
              ? Center(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 600),
                    child: content,
                  ),
                )
              : content,
        );
      },
    );
  }

  Widget _buildProgressBar(PersonalInfoProvider provider) {
    final currentStep = provider.currentStep;
    final stepCount = PersonalInfoStep.values.length;
    final progress = provider.getProgress();
    final isFirstStep = currentStep == PersonalInfoStep.personalInfo;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Add back button with navigation handling
              GestureDetector(
                onTap: () {
                  if (isFirstStep) {
                    // If on first step, navigate back to previous screen
                    Navigator.of(context).pop();
                  } else {
                    // Otherwise, go to previous step
                    provider.previousStep();
                  }
                },
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.cyan.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.cyan.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    size: 18,
                    color: AppTheme.authorityBlue,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Text(
                'ধাপ ${currentStep.index + 1} মধ্যে $stepCount',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  _getStepTitle(currentStep),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.authorityBlue,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4),
          Text(
            _getStepSubtitle(currentStep),
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          SizedBox(height: 16),
          // Progress bar
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.authorityBlue),
            minHeight: 6,
            borderRadius: BorderRadius.circular(3),
          ),
          // Step indicators
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              for (var i = 0; i < stepCount; i++)
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: i <= currentStep.index
                        ? AppTheme.authorityBlue
                        : AppTheme.authorityBlue.withOpacity(0.5),
                  ),
                  child: Center(
                    child: Text(
                      '${i + 1}',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentStep(PersonalInfoProvider provider) {
    switch (provider.currentStep) {
      case PersonalInfoStep.personalInfo:
        return Form(
          key: _personalInfoFormKey,
          child: PersonalInfoStepScreen(),
        );
      case PersonalInfoStep.nomineeInfo:
        return Form(
          key: _nomineeInfoFormKey,
          child: NomineeInfoStepScreen(),
        );
      case PersonalInfoStep.idVerification:
        return Form(
          key: _idVerificationFormKey,
          child: IdVerificationStepScreen(),
        );
      case PersonalInfoStep.bankAccount:
        return Form(
          key: _bankAccountFormKey,
          child: BankAccountStepScreen(),
        );
    }
  }

  Widget _buildNavigationButtons(PersonalInfoProvider provider) {
    final isLastStep = provider.currentStep == PersonalInfoStep.idVerification;
    final isFirstStep = provider.currentStep == PersonalInfoStep.personalInfo;
    final isVerified = provider.isVerified;

    // Only disable button if it's the last step (Submit) and user is verified
    final bool disableButton = isLastStep && isVerified;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Show verification status only on last screen if verified
          if (isLastStep && isVerified)
            Container(
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              margin: EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Your personal information has been verified. Submission is disabled.',
                      style: TextStyle(
                        color: Colors.green.shade800,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          Row(
            children: [
              // Back button
              if (!isFirstStep)
                Expanded(
                  flex: 2,
                  child: OutlinedButton.icon(
                    onPressed: () => provider.previousStep(),
                    icon: Icon(Icons.arrow_back),
                    label: Text('পেছনে'),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: AppTheme.authorityBlue),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                  ),
                ),
              // Spacer when showing both buttons
              if (!isFirstStep) SizedBox(width: 16),
              // Continue/Submit button - only disable on last screen when verified
              Expanded(
                flex: 3,
                child: ElevatedButton.icon(
                  onPressed: disableButton
                      ? null
                      : () {
                          // Skip validation if user is verified
                          if (isVerified) {
                            if (isLastStep) {
                              // Do nothing - button should be disabled
                            } else {
                              provider.nextStep();
                            }
                            return;
                          }

                          // Only run validation if user is not verified
                          if (!validateCurrentStep(provider)) {
                            String errorMessage = '';
                            switch (provider.currentStep) {
                              case PersonalInfoStep.personalInfo:
                                errorMessage =
                                    'Please fill in all personal information fields correctly';
                                break;
                              case PersonalInfoStep.nomineeInfo:
                                errorMessage =
                                    'Please fill in all nominee information fields correctly';
                                break;
                              case PersonalInfoStep.idVerification:
                                // Simple error message that doesn't check for signature
                                if (provider.frontIdImagePath == null ||
                                    provider.frontIdImagePath!.isEmpty) {
                                  errorMessage =
                                      'Please upload front side of your NID';
                                } else if (provider.backIdImagePath == null ||
                                    provider.backIdImagePath!.isEmpty) {
                                  errorMessage =
                                      'Please upload back side of your NID';
                                } else if (provider.selfieWithIdImagePath ==
                                        null ||
                                    provider.selfieWithIdImagePath!.isEmpty) {
                                  errorMessage =
                                      'Please take a selfie with your ID';
                                } else {
                                  errorMessage =
                                      'Please complete ID verification with all required documents';
                                }
                                break;
                              case PersonalInfoStep.bankAccount:
                                errorMessage =
                                    'Please fill in all bank account details correctly';
                                break;
                            }
                            showValidationError(errorMessage);
                            return;
                          }

                          if (isLastStep) {
                            _submitForm();
                          } else {
                            provider.nextStep();
                          }
                        },
                  icon: Icon(isLastStep ? Icons.check : Icons.arrow_forward),
                  label: Text(isLastStep ? 'জমা দিন' : 'পরবর্তী'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.authorityBlue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    disabledBackgroundColor: Colors.grey.shade200,
                    disabledForegroundColor: Colors.grey.shade500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getStepTitle(PersonalInfoStep step) {
    switch (step) {
      case PersonalInfoStep.personalInfo:
        return 'ব্যক্তিগত তথ্য';
      case PersonalInfoStep.nomineeInfo:
        return 'নমিনি ব্যক্তির তথ্য';
      case PersonalInfoStep.idVerification:
        return 'আইডি/পাসপোর্ট যাচাইকরণ';
      case PersonalInfoStep.bankAccount:
        return 'ব্যাংক অ্যাকাউন্ট';
    }
  }

  String _getStepSubtitle(PersonalInfoStep step) {
    switch (step) {
      case PersonalInfoStep.personalInfo:
        return 'আপনার মৌলিক বিবরণ';
      case PersonalInfoStep.nomineeInfo:
        return 'আপনার অ্যাকাউন্টের জন্য একজন নমিনি ব্যক্তি যোগ করুন';
      case PersonalInfoStep.idVerification:
        return 'আপনার আইডি/পাসপোর্ট ডকুমেন্ট আপলোড করুন';
      case PersonalInfoStep.bankAccount:
        return 'আপনার ব্যাংক অ্যাকাউন্টের বিবরণ যোগ করুন';
    }
  }

  Future<void> _submitForm() async {
    final provider = Provider.of<PersonalInfoProvider>(context, listen: false);
    final apiService = ApiService();

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(
            color: AppTheme.authorityBlue,
          ),
        ),
      );

      // Boolean values will be passed directly
      // final int outsider = provider.isExpatriate ? 1 : 0; // for expatriate
      // final int have_house = provider.hasHouse ? 1 : 0; // for house ownership
      // final int have_car = provider.hasCar ? 1 : 0; // for car ownership

      if (kIsWeb) {
        final response = await apiService.submitPersonalInfoWithImageBytes(
          name: provider.nameController.text.trim(),
          currentAddress: provider.currentAddressController.text.trim(),
          permanentAddress: provider.permanentAddressController.text
              .trim(), // Added permanentAddress
          profession: provider.professionController.text.trim(),
          income: provider.monthlyIncomeController.text.trim(),
          loanPurpose: provider.loanPurposeController.text.trim(),
          edu: provider.educationController.text.trim(),
          outsider: provider.isExpatriate, // Pass boolean directly
          have_house: provider.hasHouse, // Pass boolean directly
          have_car: provider.hasCar, // Pass boolean directly
          nomineeName: provider.nomineeNameController.text.trim(),
          nomineeRelation: provider.nomineeRelationController.text.trim(),
          nomineePhone: provider.nomineePhoneController.text.trim(),
          nidName: provider.nidNameController.text.trim(),
          nidNumber: provider.idController.text.trim(),
          bankuserName: provider.accountHolderController.text.trim(),
          bankName: provider.bankNameController.text.trim(),
          account: provider.accountNumberController.text.trim(),
          branchName: provider.ifcCodeController.text.trim(),
          selfieBytes: provider.selfieWithIdImageBytes!,
          nidFrontBytes: provider.frontIdImageBytes!,
          nidBackBytes: provider.backIdImageBytes!,
          signatureBytes: provider.signatureImageBytes,
        );

        // Close the loading dialog
        Navigator.of(context).pop();

        // Show success or error dialog based on response
        _handleSubmissionResponse(response);
        return;
      }

      // Mobile implementation - read files into memory at submission time
      try {
        // Check if we have paths to images
        if (provider.selfieWithIdImagePath == null ||
            provider.selfieWithIdImagePath!.isEmpty) {
          throw Exception('Selfie image is required');
        }
        if (provider.frontIdImagePath == null ||
            provider.frontIdImagePath!.isEmpty) {
          throw Exception('Front ID image is required');
        }
        if (provider.backIdImagePath == null ||
            provider.backIdImagePath!.isEmpty) {
          throw Exception('Back ID image is required');
        }

        // Create File objects from paths
        File selfieFile = File(provider.selfieWithIdImagePath!);
        File nidFrontFile = File(provider.frontIdImagePath!);
        File nidBackFile = File(provider.backIdImagePath!);
        File? signatureFile; // Declare as nullable File

        // Handle signature if available
        if (provider.signatureImagePath != null &&
            provider.signatureImagePath!.isNotEmpty) {
          signatureFile = File(provider.signatureImagePath!);
        }

        // Verify files exist
        if (!await selfieFile.exists()) {
          throw Exception('Selfie image file not found');
        }
        if (!await nidFrontFile.exists()) {
          throw Exception('Front ID image file not found');
        }
        if (!await nidBackFile.exists()) {
          throw Exception('Back ID image file not found');
        }

        // Make the API call for mobile platforms with actual Files
        final response = await apiService.submitPersonalInfo(
          name: provider.nameController.text.trim(),
          currentAddress: provider.currentAddressController.text.trim(),
          permanentAddress: provider.permanentAddressController.text.trim(),
          profession: provider.professionController.text.trim(),
          income: provider.monthlyIncomeController.text.trim(),
          loanPurpose: provider.loanPurposeController.text.trim(),
          outsider: provider.isExpatriate, // Pass boolean directly
          have_house: provider.hasHouse, // Pass boolean directly
          have_car: provider.hasCar, // Pass boolean directly
          nomineeName: provider.nomineeNameController.text.trim(),
          nomineeRelation: provider.nomineeRelationController.text.trim(),
          nomineePhone: provider.nomineePhoneController.text.trim(),
          nidName: provider.nidNameController.text.trim(),
          nidNumber: provider.idController.text.trim(),
          bankuserName: provider.accountHolderController.text.trim(),
          bankName: provider.bankNameController.text.trim(),
          account: provider.accountNumberController.text.trim(),
          branchName: provider.ifcCodeController.text.trim(),
          selfie: selfieFile,
          nidFrontImage: nidFrontFile,
          nidBackImage: nidBackFile,
          signature: signatureFile, // Pass nullable signatureFile directly
          edu: provider.educationController.text.trim(),
          familyMembers: provider.familyMembers.text.trim(),
        );

        // Close the loading dialog
        Navigator.of(context).pop();

        // Handle the response
        _handleSubmissionResponse(response);
      } catch (e) {
        throw Exception('Error processing mobile image files: $e');
      }
    } catch (e) {
      // Close the loading dialog
      Navigator.of(context).pop();

      // Show an error dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red),
              SizedBox(width: 8),
              Text('Error'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('An error occurred during submission:'),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  e.toString(),
                  style: TextStyle(color: Colors.red.shade800),
                ),
              ),
              SizedBox(height: 12),
              Text(
                'Please check your information and try again.',
                style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  // Helper method to validate web image paths
  bool _isValidWebImagePath(String path) {
    return path.startsWith('blob:') || path.startsWith('data:');
  }

  // Helper method to handle API response
  void _handleSubmissionResponse(dynamic response) {
    // Add debugging information
    debugPrint(
        "API Response received: success=${response.success}, message=${response.message}");

    if (response.success) {
      final provider =
          Provider.of<PersonalInfoProvider>(context, listen: false);

      // 1. Set the verified flag to true to reflect server verification
      provider.isVerified = true;

      // 2. Clear locally stored personal information data
      debugPrint(
          "Clearing personal information data after successful submission");
      provider.clearData();

      // 3. Initialize the provider to load the verified data from the API
      debugPrint("Reloading provider data from API");
      provider.initialize().then((_) {
        debugPrint("Provider data reloaded successfully");
      }).catchError((error) {
        debugPrint("Error reloading provider data: $error");
      });

      // Show a success toast message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Personal information submitted successfully!'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: Duration(seconds: 2),
        ),
      );

      // Direct navigation to loan application screen after a brief delay to allow the toast to be seen
      Future.delayed(Duration(milliseconds: 300), () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => LoanApplicationScreen(),
          ),
        );
      });
    } else {
      // Show an error dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red),
              SizedBox(width: 8),
              Text('Error'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Failed to submit your information:'),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  response.message,
                  style: TextStyle(color: Colors.red.shade800),
                ),
              ),
              SizedBox(height: 12),
              Text(
                'Please try again or contact support if the issue persists.',
                style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
