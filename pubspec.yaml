name: world_bank_loan
description: "Bangladesh Development Bank"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0+1

environment:
  sdk: ^3.5.4


dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1

  # Base packages
  cupertino_icons: ^1.0.8
  country_code_picker: ^3.1.0
  carousel_slider: ^5.0.0
  flutter_svg: ^2.0.13
  font_awesome_flutter: ^10.8.0
  image_picker: ^1.1.2
  signature: ^6.0.0
  http: ^1.2.2
  shared_preferences: ^2.3.3
  url_launcher: ^6.3.1
  permission_handler: ^11.3.0
  
  # New dependencies for modernization
  flutter_riverpod: ^2.5.0
  rive: ^0.13.5
  google_fonts: ^6.1.0
  shimmer: ^3.0.0
  lottie: ^3.1.0
  fl_chart: ^0.65.0
  flutter_animate: ^4.5.0
  flex_color_scheme: ^7.3.1
  go_router: ^13.2.1
  flutter_slidable: ^3.0.1
  flutter_hooks: ^0.21.0
  hooks_riverpod: ^2.5.0
  intl: ^0.20.2
  flutter_native_splash: ^2.4.0
  water_drop_nav_bar: ^2.2.2
  path_provider: ^2.1.2
  syncfusion_flutter_signaturepad: ^29.1.37
  firebase_messaging: ^15.2.5
  firebase_core: ^3.13.0
  connectivity_plus: ^6.1.3
  dio: ^5.8.0+1
  cached_network_image: ^3.4.1
 

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_native_splash: ^2.4.6
  flutter_lints: ^5.0.0

flutter_native_splash:
  color: "#ffffff"
  image: assets/images/splash.png


flutter:


  uses-material-design: true


  assets:
    - assets/images/credit_card.png
    - assets/icons/
    - assets/animations/
    - assets/rive/
    - assets/lottie/

