import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:world_bank_loan/core/api/api_endpoints.dart';
import '../../auth/saved_login/user_session.dart';
import '../../core/theme/app_theme.dart';
import 'package:intl/intl.dart';

class KistiDetailsScreen extends StatefulWidget {
  @override
  _KistiDetailsScreenState createState() => _KistiDetailsScreenState();
}

class _KistiDetailsScreenState extends State<KistiDetailsScreen> {
  bool isLoading = true;
  Map<String, dynamic> loanData = {};
  List<String> installmentDates = [];
  double installmentsPerMonth = 0;

  @override
  void initState() {
    super.initState();
    fetchInstallmentsDetails();
  }

  // Format date to Bangla
  String formatDate(String date) {
    DateTime parsedDate = DateTime.parse(date);
    return '${parsedDate.day}-${parsedDate.month}-${parsedDate.year}';
  }

  // Format currency
  String formatCurrency(dynamic amount) {
    if (amount == null) return '0';
    final formatter = NumberFormat('#,##,##0.00', 'en_US');
    return formatter.format(double.parse(amount.toString()));
  }

  // API কল এবং ডাটা আপডেট করার ফাংশন
  Future<void> fetchInstallmentsDetails() async {
    try {
      String? token = await UserSession.getToken();
      if (token == null) {
        throw Exception('টোকেন পাওয়া যায়নি');
      }

      final response = await http.get(
        Uri.parse(ApiEndpoints.installments),
        headers: {
          "Authorization": "Bearer $token",
        },
      );

      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        setState(() {
          loanData = {
            'totalAmount': data['totalAmount'],
            'loanDuration': data['loanDuration'],
            'totalInstallments': data['totalInstallments'],
          };
          installmentsPerMonth =
              double.parse(data['installmentsPerMonth'].toString());
          installmentDates = List<String>.from(data['installmentDates']);
          isLoading = false;
        });
      } else {
        throw Exception('ডেটা লোড করতে ব্যর্থ হয়েছে');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('ডেটা লোড করতে সমস্যা হয়েছে')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.authorityBlue,
      appBar: AppBar(
        backgroundColor: AppTheme.authorityBlue,
        elevation: 0,
        title: Text(
          'কিস্তির বিবরণ',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator(color: Colors.white))
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Loan Summary Card
                  Container(
                    margin: EdgeInsets.all(16),
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ঋণের সারসংক্ষেপ',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.authorityBlue,
                          ),
                        ),
                        SizedBox(height: 16),
                        _buildInfoRow('মোট ঋণের পরিমাণ',
                            '৳ ${formatCurrency(loanData['totalAmount'])}'),
                        _buildInfoRow(
                            'মেয়াদ', '${loanData['loanDuration']} মাস'),
                        _buildInfoRow('মোট কিস্তি সংখ্যা',
                            '${loanData['totalInstallments']}টি'),
                        _buildInfoRow('মাসিক কিস্তি',
                            '৳ ${formatCurrency(installmentsPerMonth)}'),
                      ],
                    ),
                  ),

                  // Installments List
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: ListView.builder(
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: installmentDates.length,
                      itemBuilder: (context, index) {
                        return Container(
                          margin:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey.shade200),
                            ),
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'কিস্তি #${index + 1}',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: AppTheme.authorityBlue,
                                        ),
                                      ),
                                      SizedBox(height: 4),
                                      Text(
                                        'তারিখ: ${formatDate(installmentDates[index])}',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        '৳ ${formatCurrency(installmentsPerMonth)}',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: AppTheme.authorityBlue,
                                        ),
                                      ),
                                      SizedBox(height: 4),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.blue.shade50,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          'বাকি আছে',
                                          style: TextStyle(
                                            color: AppTheme.authorityBlue,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 16),
                ],
              ),
            ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
