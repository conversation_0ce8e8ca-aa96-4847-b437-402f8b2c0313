import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class CardCache {
  static const String CARD_DATA_KEY = 'card_data';
  static const String WITHDRAW_LIST_KEY = 'withdraw_list';
  static const Duration CACHE_DURATION = Duration(minutes: 15);

  SharedPreferences? _prefs;
  bool _initialized = false;

  CardCache() {
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    if (!_initialized) {
      _prefs = await SharedPreferences.getInstance();
      _initialized = true;
    }
  }

  Future<void> cacheCardData(Map<String, dynamic> data) async {
    await _initPrefs();

    final cacheEntry = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'data': data,
    };
    await _prefs?.setString(CARD_DATA_KEY, json.encode(cacheEntry));
  }

  Future<void> cacheWithdrawList(List<dynamic> withdrawList) async {
    await _initPrefs();

    final cacheEntry = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'data': withdrawList,
    };
    await _prefs?.setString(WITHDRAW_LIST_KEY, json.encode(cacheEntry));
  }

  Future<Map<String, dynamic>?> getCachedCardData() async {
    await _initPrefs();

    final cachedString = _prefs?.getString(CARD_DATA_KEY);
    if (cachedString != null) {
      final cacheEntry = json.decode(cachedString);
      final timestamp = cacheEntry['timestamp'] as int;

      if (DateTime.now().millisecondsSinceEpoch - timestamp <
          CACHE_DURATION.inMilliseconds) {
        return cacheEntry['data'];
      }
    }
    return null;
  }

  Future<List<dynamic>?> getCachedWithdrawList() async {
    await _initPrefs();

    final cachedString = _prefs?.getString(WITHDRAW_LIST_KEY);
    if (cachedString != null) {
      final cacheEntry = json.decode(cachedString);
      final timestamp = cacheEntry['timestamp'] as int;

      if (DateTime.now().millisecondsSinceEpoch - timestamp <
          CACHE_DURATION.inMilliseconds) {
        return cacheEntry['data'];
      }
    }
    return null;
  }

  Future<void> clearCache() async {
    await _initPrefs();
    await _prefs?.remove(CARD_DATA_KEY);
    await _prefs?.remove(WITHDRAW_LIST_KEY);
  }
}
