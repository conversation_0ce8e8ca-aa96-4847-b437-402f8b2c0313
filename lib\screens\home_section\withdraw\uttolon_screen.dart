import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:world_bank_loan/core/theme/app_theme.dart';
import 'package:world_bank_loan/providers/withdraw_request_provider.dart';

class UttolonScreen extends StatefulWidget {
  const UttolonScreen({super.key});

  @override
  State<UttolonScreen> createState() => _UttolonScreenState();
}

class _UttolonScreenState extends State<UttolonScreen> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _secretCodeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _amountController.dispose();
    _secretCodeController.dispose();
    super.dispose();
  }

  String? _validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'এমাউন্ট আবশ্যক';
    }
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return 'দয়া করে একটি বৈধ সংখ্যা লিখুন';
    }
    return null;
  }

  String? _validateSecretCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'উত্তোলন কোড আবশ্যক';
    }
    if (value.length < 2) {
      return 'উত্তোলন কোড কমপক্ষে 2 অক্ষর হতে হবে';
    }
    return null;
  }

  Future<void> _processWithdrawal(BuildContext context) async {
    if (_formKey.currentState!.validate()) {
      final provider =
          Provider.of<WithdrawRequestProvider>(context, listen: false);
      await provider.submitWithdrawRequest(
        amount: _amountController.text,
        pin: _secretCodeController.text,
        context: context,
      );
      if (provider.successMessage != null) {
        _amountController.clear();
        _secretCodeController.clear();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(provider.successMessage!),
            backgroundColor: Colors.green,
          ),
        );
        provider.clearMessages();
        Navigator.pop(context);
      } else if (provider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(provider.errorMessage!),
            backgroundColor: Colors.redAccent,
          ),
        );
        provider.clearMessages();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => WithdrawRequestProvider(),
      child: Consumer<WithdrawRequestProvider>(
        builder: (context, provider, _) {
          return Scaffold(
            appBar: PreferredSize(
              preferredSize: const Size.fromHeight(kToolbarHeight),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final isDesktop = constraints.maxWidth >= 800;
                  final double contentWidth = isDesktop ? 600 : double.infinity;
                  return Center(
                    child: SizedBox(
                      width: contentWidth,
                      child: AppBar(
                        title: const Text(
                          'উত্তোলন',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        backgroundColor: AppTheme.authorityBlue,
                        elevation: 0,
                        iconTheme: const IconThemeData(color: Colors.white),
                      ),
                    ),
                  );
                },
              ),
            ),
            body: Form(
              key: _formKey,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final isDesktop = constraints.maxWidth >= 800;
                  final double contentWidth = isDesktop ? 600 : double.infinity;
                  return Center(
                    child: SizedBox(
                      width: contentWidth,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Info Card
                            _buildInfoCard(),
                            const SizedBox(height: 24),

                            // Amount Field
                            _buildTextField(
                              label: 'এমাউন্ট',
                              controller: _amountController,
                              prefixIcon: Icons.money_outlined,
                              keyboardType: TextInputType.number,
                              validator: _validateAmount,
                            ),
                            const SizedBox(height: 16),

                            // Secret Code Field
                            _buildTextField(
                              label: 'উত্তোলন কোড',
                              controller: _secretCodeController,
                              prefixIcon: Icons.lock_outline,
                              isPassword: true,
                              validator: _validateSecretCode,
                            ),
                            const SizedBox(height: 32),

                            // Withdrawal Button
                            ElevatedButton(
                              onPressed: provider.isLoading
                                  ? null
                                  : () => _processWithdrawal(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.authorityBlue,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 2,
                              ),
                              child: provider.isLoading
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Text(
                                      'উত্তোলন',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.authorityBlue, AppTheme.authorityBlue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.white,
              ),
              SizedBox(width: 8),
              Text(
                'গুরুত্বপূর্ণ তথ্য',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'আপনার উত্তোলন অনুরোধ প্রক্রিয়া করতে উত্তোলন কোড ব্যবহার করুন। যেকোনো সমস্যার জন্য আমাদের হেল্পলাইনে যোগাযোগ করুন।',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required IconData? prefixIcon,
    bool isPassword = false,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: isPassword,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Icon(prefixIcon),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          errorStyle: TextStyle(
            color: Colors.red.shade400,
            fontSize: 12,
          ),
        ),
        validator: validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
      ),
    );
  }
}
