import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:world_bank_loan/core/theme/app_theme.dart';
import 'package:world_bank_loan/core/performance/performance_config.dart';
import 'package:world_bank_loan/providers/app_provider.dart';

import 'package:world_bank_loan/screens/splash_screen/splash_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:world_bank_loan/services/notification_service.dart';
import 'package:world_bank_loan/services/connectivity_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize performance optimizations
  PerformanceConfig.initialize();

  // Initialize Firebase asynchronously to avoid blocking main thread
  _initializeFirebaseAsync();

  runApp(
    ProviderScope(
      child: AppProviders(
        child: const MyApp(),
      ),
    ),
  );
}

// Initialize Firebase and services asynchronously
Future<void> _initializeFirebaseAsync() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize services after Firebase is ready
    final notificationService = NotificationService();
    final connectivityService = ConnectivityService();

    // Initialize services in parallel
    await Future.wait([
      notificationService.initialize(),
      Future.microtask(() => connectivityService.initialize()),
    ]);
  } catch (e) {
    debugPrint('Error initializing Firebase or services: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      navigatorKey: navigatorKey,
      theme: AppTheme.lightTheme(useBanglaFont: true).copyWith(
        appBarTheme: AppBarTheme(
          foregroundColor: Colors.white,
          backgroundColor: AppTheme.authorityBlue,
          titleTextStyle: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
            fontFamily: GoogleFonts.hindSiliguri().fontFamily,
          ),
          iconTheme: IconThemeData(color: Colors.white),
          actionsIconTheme: IconThemeData(color: Colors.white),
        ),
      ),
      builder: (context, child) {
        // Add the ConnectivityBanner at the app level
        return MediaQuery(
          // Set data in MediaQuery to ensure proper sizing
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.noScaling,
          ),
          child: child!,
        );
      },
      home: SplashScreen(),
    );
  }
}
