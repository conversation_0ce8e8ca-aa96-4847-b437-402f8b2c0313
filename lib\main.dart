import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:world_bank_loan/core/theme/app_theme.dart';
import 'package:world_bank_loan/providers/app_provider.dart';

import 'package:world_bank_loan/screens/splash_screen/splash_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:world_bank_loan/services/notification_service.dart';
import 'package:world_bank_loan/services/connectivity_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize notification service
  final notificationService = NotificationService();
  await notificationService.initialize();

  // Initialize connectivity service
  final connectivityService = ConnectivityService();
  connectivityService.initialize();

  runApp(
    ProviderScope(
      child: AppProviders(
        child: <PERSON>App(),
      ),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      navigatorKey: navigatorKey,
      theme: AppTheme.lightTheme(useBanglaFont: true).copyWith(
        appBarTheme: AppBarTheme(
          foregroundColor: Colors.white,
          backgroundColor: AppTheme.authorityBlue,
          titleTextStyle: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
            fontFamily: GoogleFonts.hindSiliguri().fontFamily,
          ),
          iconTheme: IconThemeData(color: Colors.white),
          actionsIconTheme: IconThemeData(color: Colors.white),
        ),
      ),
      builder: (context, child) {
        // Add the ConnectivityBanner at the app level
        return MediaQuery(
          // Set data in MediaQuery to ensure proper sizing
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.noScaling,
          ),
          child: child!,
        );
      },
      home: SplashScreen(),
    );
  }
}
