import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:world_bank_loan/auth/saved_login/user_session.dart';
import 'package:world_bank_loan/core/api/api_endpoints.dart';
import 'package:world_bank_loan/core/cache/card_cache.dart';

enum CardLoadingStatus { initial, loading, loaded, error }

class CardProvider extends ChangeNotifier {
  CardLoadingStatus _status = CardLoadingStatus.initial;
  String _cardNumber = '';
  String _cardHolderName = '';
  String _validity = '';
  String _cvv = '';
  String _userBankName = '';
  String _userBankNumber = '';
  String? _errorMessage;

  List<dynamic> _withdrawList = [];
  String _walletBalance = "0";
  String _walletLoan = "0";

  // Pagination variables
  final int _itemsPerPage = 10;
  int _currentPage = 1;
  bool _hasMoreItems = true;
  bool _isLoadingMore = false;

  late CardCache _cache;
  Timer? _debounceTimer;
  bool _isLoading = false;

  // Getters
  CardLoadingStatus get status => _status;
  String get cardNumber => _cardNumber;
  String get cardHolderName => _cardHolderName;
  String get validity => _validity;
  String get cvv => _cvv;
  String get userBankName => _userBankName;
  String get userBankNumber => _userBankNumber;
  String? get errorMessage => _errorMessage;

  List<dynamic> get withdrawList => _withdrawList;
  String get walletBalance => _walletBalance;
  String get walletLoan => _walletLoan;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasMoreItems => _hasMoreItems;

  CardProvider() {
    // Initialize cache immediately
    _cache = CardCache();

    // Load cached data
    _loadCachedData();
  }

  Future<void> _loadCachedData() async {
    try {
      final cachedData = await _cache.getCachedCardData();
      if (cachedData != null) {
        _updateFromCache(cachedData);
        notifyListeners();
      }

      final cachedWithdraws = await _cache.getCachedWithdrawList();
      if (cachedWithdraws != null) {
        _withdrawList = cachedWithdraws;
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) print('Error loading cached data: $e');
    }
  }

  // Fetch card data from the API with caching
  Future<void> fetchCardData() async {
    if (_isLoading) return;
    _isLoading = true;

    try {
      _status = CardLoadingStatus.loading;
      notifyListeners();

      // Try to get cached data first
      final cachedData = await _cache.getCachedCardData();
      if (cachedData != null) {
        _updateFromCache(cachedData);
        notifyListeners();
      }

      // Fetch fresh data
      final token = await UserSession.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }
      debugPrint('token: $token');

      final response = await http.get(
        Uri.parse(ApiEndpoints.card),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);

        // Cache the new data
        await _cache.cacheCardData(jsonResponse);

        // Update the UI with new data
        _updateFromResponse(jsonResponse);

        // Fetch additional data
        await Future.wait([fetchWithdrawData(), fetchWithdrawDetails()]);
      } else {
        _errorMessage = 'Failed to load banking data: ${response.statusCode}';
        _status = CardLoadingStatus.error;
      }
    } catch (e) {
      _errorMessage = 'Error: ${e.toString()}';
      _status = CardLoadingStatus.error;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _updateFromCache(Map<String, dynamic> data) {
    _userBankName = data['userBankName'] ?? '';
    _userBankNumber = data['userBankNumber'] ?? '';

    if (data['cards'] != null && data['cards'].isNotEmpty) {
      final cardData = data['cards'][0];
      _cardNumber = cardData['cardNumber'] ?? '';
      _cardHolderName = cardData['cardHolderName'] ?? '';
      _validity = cardData['validity'] ?? '';
      _cvv = cardData['cvv'] ?? '';
    }

    _status = CardLoadingStatus.loaded;
  }

  void _updateFromResponse(Map<String, dynamic> data) {
    _updateFromCache(data);
  }

  void debounceUpdate(VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), callback);
  }

  // Fetch withdraw data from API with pagination
  Future<void> fetchWithdrawData({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreItems = true;
      _withdrawList = [];
      notifyListeners();
    }

    if (!_hasMoreItems || _isLoadingMore) return;

    try {
      _isLoadingMore = true;
      notifyListeners();

      final token = await UserSession.getToken();
      if (token == null) {
        _errorMessage = 'আপনি লগ ইন করা নেই';
        notifyListeners();
        return;
      }

      final response = await http.get(
        Uri.parse(
            '${ApiEndpoints.getWithdrawAcceptList}?page=$_currentPage&limit=$_itemsPerPage'),
        headers: {'Authorization': 'Bearer $token'},
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () => throw TimeoutException('Request timed out'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final newItems = data['withdraws'] ?? [];

        if (newItems.isEmpty) {
          _hasMoreItems = false;
        } else {
          _withdrawList.addAll(newItems);
          _currentPage++;

          // Cache the updated withdraw list
          await _cache.cacheWithdrawList(_withdrawList);
        }
      } else if (response.statusCode == 401) {
        _errorMessage = 'আপনার সেশন মেয়াদ শেষ হয়েছে';
      } else {
        _errorMessage = 'সার্ভার ত্রুটি: ${response.statusCode}';
      }
    } catch (e) {
      if (e is TimeoutException) {
        _errorMessage = 'অনুরোধের সময়সীমা শেষ হয়েছে';
      } else {
        _errorMessage = 'সংযোগ ত্রুটি: $e';
      }
      if (kDebugMode) print('Error fetching withdraws: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Refresh withdraw data
  Future<void> refreshWithdraws() async {
    _errorMessage = null;
    await fetchWithdrawData(refresh: true);
  }

  // Load more withdraws
  Future<void> loadMoreWithdraws() async {
    if (!_isLoadingMore && _hasMoreItems) {
      await fetchWithdrawData();
    }
  }

  // Fetch wallet balance and loan
  Future<void> fetchWithdrawDetails() async {
    try {
      final token = await UserSession.getToken();
      if (token == null) return;
      final response = await http.get(
        Uri.parse(ApiEndpoints.withdrawMethods),
        headers: {'Authorization': 'Bearer $token'},
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _walletBalance = data['userBankInfo']['balance'].toString();
        _walletLoan = data['userBankInfo']['loanBalance'].toString();
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) print('Error fetching withdraw details: $e');
    }
  }
}
