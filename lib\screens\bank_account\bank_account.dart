import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:world_bank_loan/screens/loan_apply_screen/loan_apply_screen.dart';
import 'dart:convert';
import '../../auth/saved_login/user_session.dart';
import 'package:world_bank_loan/core/api/api_endpoints.dart';
import 'package:provider/provider.dart';
import '../../providers/personal_info_provider.dart';

class BankAccountScreen extends StatefulWidget {
  const BankAccountScreen({super.key});

  @override
  _BankAccountScreenState createState() => _BankAccountScreenState();
}

class _BankAccountScreenState extends State<BankAccountScreen> {
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // No need to fetch here, provider handles initialization
  }

  @override
  void dispose() {
    // No need to dispose controllers here, provider handles it
    // accountNumberController.dispose();
    // accountHolderController.dispose();
    // bankNameController.dispose();
    // ifcCode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Container(
              constraints: constraints.maxWidth >= 800
                  ? const BoxConstraints(maxWidth: 600)
                  : null,
              width: double.infinity,
              child: AppBar(
                title: Text('ব্যাংক অ্যাকাউন্ট'),
              ),
            );
          },
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            constraints: constraints.maxWidth >= 800
                ? const BoxConstraints(maxWidth: 600)
                : null,
            width: double.infinity,
            child: Consumer<PersonalInfoProvider>(
              builder: (context, provider, _) {
                return SingleChildScrollView(
                  padding: EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 16.0),
                        _buildTextField(
                            'অ্যাকাউন্ট হোল্ডারের নাম',
                            provider.accountHolderController,
                            provider.isVerified),
                        SizedBox(height: 8.0),
                        _buildTextField('ব্যাংকের নাম',
                            provider.bankNameController, provider.isVerified),
                        SizedBox(height: 8.0),
                        _buildTextField(
                            'অ্যাকাউন্ট নম্বর',
                            provider.accountNumberController,
                            provider.isVerified,
                            keyboardType: TextInputType.number),
                        SizedBox(height: 8.0),
                        _buildTextField('শাখার নাম', provider.ifcCodeController,
                            provider.isVerified),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  // Section Title Widget
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  // TextField Widget
  Widget _buildTextField(
    String label,
    TextEditingController controller,
    bool isVerified, // Pass isVerified status
    {
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      enabled: !isVerified, // Enable if NOT verified
      readOnly: isVerified, // Make read-only if verified
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(),
        suffixIcon:
            isVerified ? Icon(Icons.lock, size: 16, color: Colors.grey) : null,
      ),
      validator: (value) {
        if (!isVerified && (value == null || value.isEmpty)) {
          // Only validate if not verified
          return 'এই ক্ষেত্রটি আবশ্যক';
        }
        return null;
      },
    );
  }
}
