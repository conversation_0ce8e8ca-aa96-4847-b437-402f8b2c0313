import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Performance testing utilities for measuring app performance
class PerformanceTester {
  static final PerformanceTester _instance = PerformanceTester._internal();
  factory PerformanceTester() => _instance;
  PerformanceTester._internal();

  final Map<String, Stopwatch> _stopwatches = {};
  final Map<String, List<int>> _measurements = {};
  bool _isEnabled = kDebugMode;

  /// Enable or disable performance testing
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Start measuring a performance metric
  void startMeasurement(String name) {
    if (!_isEnabled) return;
    
    _stopwatches[name] = Stopwatch()..start();
  }

  /// Stop measuring and record the result
  int stopMeasurement(String name) {
    if (!_isEnabled) return 0;
    
    final stopwatch = _stopwatches[name];
    if (stopwatch == null) return 0;
    
    stopwatch.stop();
    final elapsed = stopwatch.elapsedMilliseconds;
    
    _measurements.putIfAbsent(name, () => []).add(elapsed);
    _stopwatches.remove(name);
    
    if (kDebugMode) {
      debugPrint('Performance: $name took ${elapsed}ms');
    }
    
    return elapsed;
  }

  /// Measure a function execution time
  Future<T> measureAsync<T>(String name, Future<T> Function() function) async {
    if (!_isEnabled) return await function();
    
    startMeasurement(name);
    try {
      final result = await function();
      return result;
    } finally {
      stopMeasurement(name);
    }
  }

  /// Measure a synchronous function execution time
  T measureSync<T>(String name, T Function() function) {
    if (!_isEnabled) return function();
    
    startMeasurement(name);
    try {
      return function();
    } finally {
      stopMeasurement(name);
    }
  }

  /// Get performance statistics for a metric
  PerformanceStats? getStats(String name) {
    final measurements = _measurements[name];
    if (measurements == null || measurements.isEmpty) return null;
    
    measurements.sort();
    final count = measurements.length;
    final sum = measurements.reduce((a, b) => a + b);
    final average = sum / count;
    final median = count % 2 == 0
        ? (measurements[count ~/ 2 - 1] + measurements[count ~/ 2]) / 2
        : measurements[count ~/ 2].toDouble();
    
    return PerformanceStats(
      name: name,
      count: count,
      average: average,
      median: median,
      min: measurements.first,
      max: measurements.last,
      total: sum,
    );
  }

  /// Get all performance statistics
  Map<String, PerformanceStats> getAllStats() {
    final stats = <String, PerformanceStats>{};
    for (final name in _measurements.keys) {
      final stat = getStats(name);
      if (stat != null) {
        stats[name] = stat;
      }
    }
    return stats;
  }

  /// Clear all measurements
  void clearMeasurements() {
    _measurements.clear();
    _stopwatches.clear();
  }

  /// Print performance report
  void printReport() {
    if (!_isEnabled) return;
    
    debugPrint('\n=== Performance Report ===');
    final stats = getAllStats();
    
    if (stats.isEmpty) {
      debugPrint('No performance data available');
      return;
    }
    
    for (final stat in stats.values) {
      debugPrint('${stat.name}:');
      debugPrint('  Count: ${stat.count}');
      debugPrint('  Average: ${stat.average.toStringAsFixed(2)}ms');
      debugPrint('  Median: ${stat.median.toStringAsFixed(2)}ms');
      debugPrint('  Min: ${stat.min}ms');
      debugPrint('  Max: ${stat.max}ms');
      debugPrint('  Total: ${stat.total}ms');
      debugPrint('');
    }
    debugPrint('========================\n');
  }

  /// Measure widget build performance
  Widget measureWidgetBuild(String name, Widget child) {
    if (!_isEnabled) return child;
    
    return _PerformanceMeasureWidget(
      name: name,
      child: child,
    );
  }

  /// Measure frame rate
  void startFrameRateMeasurement() {
    if (!_isEnabled) return;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureFrameRate();
    });
  }

  void _measureFrameRate() {
    final stopwatch = Stopwatch()..start();
    int frameCount = 0;
    
    void frameCallback(Duration timestamp) {
      frameCount++;
      if (stopwatch.elapsedMilliseconds >= 1000) {
        final fps = frameCount / (stopwatch.elapsedMilliseconds / 1000);
        debugPrint('FPS: ${fps.toStringAsFixed(1)}');
        
        // Reset for next measurement
        stopwatch.reset();
        frameCount = 0;
        stopwatch.start();
      }
      
      WidgetsBinding.instance.addPostFrameCallback(frameCallback);
    }
    
    WidgetsBinding.instance.addPostFrameCallback(frameCallback);
  }

  /// Measure memory usage
  void measureMemoryUsage(String context) {
    if (!_isEnabled) return;
    
    developer.Timeline.startSync('Memory Usage - $context');
    
    // Force garbage collection for more accurate measurement
    developer.Timeline.finishSync();
    
    debugPrint('Memory measurement point: $context');
  }

  /// Measure network request performance
  Future<T> measureNetworkRequest<T>(
    String endpoint,
    Future<T> Function() request,
  ) async {
    return measureAsync('Network: $endpoint', request);
  }

  /// Measure app startup time
  static void measureAppStartup() {
    if (!kDebugMode) return;
    
    final instance = PerformanceTester();
    instance.startMeasurement('App Startup');
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      instance.stopMeasurement('App Startup');
    });
  }
}

/// Performance statistics data class
class PerformanceStats {
  final String name;
  final int count;
  final double average;
  final double median;
  final int min;
  final int max;
  final int total;

  const PerformanceStats({
    required this.name,
    required this.count,
    required this.average,
    required this.median,
    required this.min,
    required this.max,
    required this.total,
  });

  @override
  String toString() {
    return 'PerformanceStats(name: $name, count: $count, avg: ${average.toStringAsFixed(2)}ms, median: ${median.toStringAsFixed(2)}ms, min: ${min}ms, max: ${max}ms)';
  }
}

/// Widget for measuring build performance
class _PerformanceMeasureWidget extends StatelessWidget {
  final String name;
  final Widget child;

  const _PerformanceMeasureWidget({
    required this.name,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final tester = PerformanceTester();
    tester.startMeasurement('Widget Build: $name');
    
    return Builder(
      builder: (context) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          tester.stopMeasurement('Widget Build: $name');
        });
        return child;
      },
    );
  }
}

/// Mixin for automatic performance measurement in StatefulWidgets
mixin PerformanceMeasureMixin<T extends StatefulWidget> on State<T> {
  late String _widgetName;

  @override
  void initState() {
    super.initState();
    _widgetName = widget.runtimeType.toString();
    PerformanceTester().startMeasurement('Widget Lifecycle: $_widgetName');
  }

  @override
  void dispose() {
    PerformanceTester().stopMeasurement('Widget Lifecycle: $_widgetName');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PerformanceTester().measureWidgetBuild(
      'Widget Build: $_widgetName',
      buildWidget(context),
    );
  }

  /// Override this instead of build()
  Widget buildWidget(BuildContext context);
}

/// Performance testing utilities for specific scenarios
class PerformanceTestScenarios {
  static final PerformanceTester _tester = PerformanceTester();

  /// Test list scrolling performance
  static void testListScrolling(ScrollController controller) {
    if (!kDebugMode) return;
    
    controller.addListener(() {
      _tester.measureSync('List Scroll', () {
        // Measure scroll performance
      });
    });
  }

  /// Test navigation performance
  static void testNavigation(String routeName) {
    _tester.startMeasurement('Navigation: $routeName');
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _tester.stopMeasurement('Navigation: $routeName');
    });
  }

  /// Test animation performance
  static void testAnimation(String animationName, AnimationController controller) {
    controller.addListener(() {
      if (controller.isAnimating) {
        _tester.measureSync('Animation Frame: $animationName', () {
          // Measure animation frame performance
        });
      }
    });
  }
}
