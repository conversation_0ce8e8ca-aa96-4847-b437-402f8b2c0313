// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDLj5G995UhMKnWXA682XXbG67RmKrR-9I',
    appId: '1:************:web:84b0ec793f929e7a05b9f9',
    messagingSenderId: '************',
    projectId: 'worldbank-9c678',
    authDomain: 'worldbank-9c678.firebaseapp.com',
    storageBucket: 'worldbank-9c678.firebasestorage.app',
    measurementId: 'G-58TBGF7GKY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAeqCpONPvedXeuBvodpeNdrhMiCxKfXBY',
    appId: '1:************:android:74b8d2424250099d05b9f9',
    messagingSenderId: '************',
    projectId: 'worldbank-9c678',
    storageBucket: 'worldbank-9c678.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCLBvpFZifikodtbrFfG3Yaiz_87lRdrkE',
    appId: '1:************:ios:2a58055822a57d5f05b9f9',
    messagingSenderId: '************',
    projectId: 'worldbank-9c678',
    storageBucket: 'worldbank-9c678.firebasestorage.app',
    iosBundleId: 'com.bankloan.worldBankLoan',
  );
}
