import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Performance configuration and optimization utilities
class PerformanceConfig {
  // Private constructor to prevent instantiation
  PerformanceConfig._();

  /// Initialize performance optimizations
  static void initialize() {
    // Enable performance overlay in debug mode
    if (kDebugMode) {
      debugPaintSizeEnabled = false; // Disable by default, can be enabled for debugging
    }

    // Configure system UI for better performance
    _configureSystemUI();

    // Set up memory management
    _configureMemoryManagement();
  }

  /// Configure system UI optimizations
  static void _configureSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations for better performance
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// Configure memory management settings
  static void _configureMemoryManagement() {
    // Configure image cache
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50 MB
  }

  /// Performance monitoring utilities
  static void enablePerformanceOverlay() {
    if (kDebugMode) {
      debugPaintSizeEnabled = true;
    }
  }

  static void disablePerformanceOverlay() {
    if (kDebugMode) {
      debugPaintSizeEnabled = false;
    }
  }

  /// Memory cleanup utilities
  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
  }

  static void clearLiveImages() {
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// Network optimization settings
  static const Duration networkTimeout = Duration(seconds: 15);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  /// Animation optimization settings
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  /// List view optimization settings
  static const double listCacheExtent = 250.0;
  static const int listItemCacheSize = 50;

  /// Timer optimization settings
  static const Duration backgroundRefreshInterval = Duration(seconds: 30);
  static const Duration foregroundRefreshInterval = Duration(seconds: 10);
  static const Duration debounceDelay = Duration(milliseconds: 300);

  /// Build optimization flags
  static const bool enableRepaintBoundaries = true;
  static const bool enableAutomaticKeepAlives = false;
  static const bool enableSemantics = true;

  /// Performance thresholds
  static const int maxConcurrentRequests = 3;
  static const int maxCacheSize = 100;
  static const Duration maxLoadingTime = Duration(seconds: 10);
}

/// Widget performance utilities
class PerformanceUtils {
  PerformanceUtils._();

  /// Create a performance-optimized RepaintBoundary
  static Widget repaintBoundary({required Widget child}) {
    return RepaintBoundary(child: child);
  }

  /// Create a performance-optimized AutomaticKeepAlive
  static Widget keepAlive({
    required Widget child,
    bool wantKeepAlive = true,
  }) {
    return AutomaticKeepAliveClientMixin.wantKeepAlive
        ? child
        : child;
  }

  /// Debounce function calls for performance
  static void debounce(
    VoidCallback callback, {
    Duration delay = PerformanceConfig.debounceDelay,
  }) {
    Timer? timer;
    timer?.cancel();
    timer = Timer(delay, callback);
  }

  /// Throttle function calls for performance
  static void throttle(
    VoidCallback callback, {
    Duration interval = PerformanceConfig.debounceDelay,
  }) {
    bool canExecute = true;
    if (canExecute) {
      canExecute = false;
      callback();
      Timer(interval, () {
        canExecute = true;
      });
    }
  }

  /// Check if device is low-end for performance adjustments
  static bool get isLowEndDevice {
    // Simple heuristic based on available memory
    // In a real app, you might want to use device_info_plus package
    return false; // Placeholder - implement based on your needs
  }

  /// Get optimized animation duration based on device performance
  static Duration getOptimizedAnimationDuration({
    Duration? defaultDuration,
  }) {
    if (isLowEndDevice) {
      return PerformanceConfig.fastAnimation;
    }
    return defaultDuration ?? PerformanceConfig.normalAnimation;
  }

  /// Create performance-optimized scroll physics
  static ScrollPhysics get optimizedScrollPhysics {
    return const BouncingScrollPhysics(
      parent: AlwaysScrollableScrollPhysics(),
    );
  }

  /// Create performance-optimized list view settings
  static Widget optimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: optimizedScrollPhysics,
      cacheExtent: PerformanceConfig.listCacheExtent,
      addAutomaticKeepAlives: PerformanceConfig.enableAutomaticKeepAlives,
      addRepaintBoundaries: PerformanceConfig.enableRepaintBoundaries,
      addSemanticIndexes: PerformanceConfig.enableSemantics,
    );
  }
}

/// Performance monitoring mixin
mixin PerformanceMonitorMixin<T extends StatefulWidget> on State<T> {
  late Stopwatch _stopwatch;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      _stopwatch = Stopwatch()..start();
    }
  }

  @override
  void dispose() {
    if (kDebugMode) {
      _stopwatch.stop();
      debugPrint('${widget.runtimeType} lifecycle: ${_stopwatch.elapsedMilliseconds}ms');
    }
    super.dispose();
  }

  /// Measure build performance
  void measureBuildPerformance(VoidCallback buildFunction) {
    if (kDebugMode) {
      final stopwatch = Stopwatch()..start();
      buildFunction();
      stopwatch.stop();
      if (stopwatch.elapsedMilliseconds > 16) {
        debugPrint('Slow build detected in ${widget.runtimeType}: ${stopwatch.elapsedMilliseconds}ms');
      }
    } else {
      buildFunction();
    }
  }
}

/// Timer management for performance
class PerformanceTimer {
  static final Map<String, Timer> _timers = {};

  /// Create or update a named timer
  static void setTimer(
    String name,
    Duration duration,
    VoidCallback callback,
  ) {
    cancelTimer(name);
    _timers[name] = Timer(duration, callback);
  }

  /// Create or update a periodic timer
  static void setPeriodicTimer(
    String name,
    Duration duration,
    void Function(Timer) callback,
  ) {
    cancelTimer(name);
    _timers[name] = Timer.periodic(duration, callback);
  }

  /// Cancel a named timer
  static void cancelTimer(String name) {
    _timers[name]?.cancel();
    _timers.remove(name);
  }

  /// Cancel all timers
  static void cancelAllTimers() {
    for (final timer in _timers.values) {
      timer.cancel();
    }
    _timers.clear();
  }

  /// Check if timer exists
  static bool hasTimer(String name) {
    return _timers.containsKey(name) && _timers[name]!.isActive;
  }
}
