import 'package:flutter/foundation.dart';

class ApiEndpoints {
  // Base URL for all endpoints
  static const String baseUrl = "https://bblonlineloan.com/api";

  // Authentication Endpoints
  static String get login => "$baseUrl/login";
  static String get register => "$baseUrl/register";
  static String get changePassword => "$baseUrl/change-password";

  // User Profile Endpoints
  static String get index => "$baseUrl/index";
  static String get profile => "$baseUrl/index";

  //notification
  static String get notification => "$baseUrl/notifications";

  //personal info  verify
  static String get personalInfoVerify => "$baseUrl/verify";
  static String get getPersonalInfoVerify => "$baseUrl/getverified";
  // Loan Management Endpoints
  static String get loans => "$baseUrl/loans";
  static String get certificate => "$baseUrl/certificate";

  //  Withdraw Endpoints
  static String get withdrawMethods => "$baseUrl/method";

  //upload screenshot
  static String get uploadScreenshot => "$baseUrl/recharge";

  // Withdraw Endpoints
  static String get withdraw => "$baseUrl/withdraw";
  static String get getBank => "$baseUrl/getbank";
  static String get saveBank => "$baseUrl/savebank";
  static String get card => "$baseUrl/card";
  static String get getWithdrawAcceptList => "$baseUrl/getWithdraws";

  // Content Endpoints
  static String get slides => "$baseUrl/slides";

  // Support & Help Endpoints
  static String get about => "$baseUrl/about";
  static String get support => "$baseUrl/support";
  static String get complaint => "$baseUrl/complaint";
  static String get userAgreement => "$baseUrl/aggrement";
  static String get installments => "$baseUrl/installments";
  // Helper method to get full URL with path
  static String getUrl(String path) {
    return "$baseUrl/$path";
  }

  // Debug method to print all endpoints (useful for development)
  static void printAllEndpoints() {
    if (kDebugMode) {
      print('=== API ENDPOINTS ===');
      print('Base URL: $baseUrl');
      print('--- Authentication ---');
      print('Login: $login');
      print('Register: $register');
      print('Change Password: $changePassword');
      print('--- Profile ---');
      print('Index: $index');
      print('--- Loan Management ---');
      print('Loans: $loans');
      print('Certificate: $certificate');
      print('--- Banking ---');
      print('Withdraw Methods: $withdrawMethods');
      print('Withdraw: $withdraw');
      print('Get Bank: $getBank');
      print('Save Bank: $saveBank');
      print('Card: $card');
      print('--- Content ---');
      print('Slides: $slides');
      print('--- Support ---');
      print('About: $about');
      print('Support: $support');
      print('Complaint: $complaint');
      print('====================');
    }
  }
}
