import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:world_bank_loan/core/api/api_service.dart';
import 'package:flutter/foundation.dart';

enum PersonalInfoStep {
  personalInfo,
  nomineeInfo,
  bankAccount, // Moved up
  idVerification, // Moved down
}

class PersonalInfoProvider extends ChangeNotifier {
  // Form controllers for personal information
  final TextEditingController nameController = TextEditingController();
  final TextEditingController currentAddressController =
      TextEditingController();
  final TextEditingController permanentAddressController =
      TextEditingController(); // Added permanent address
  final TextEditingController familyMembers = TextEditingController();

  // Expatriate status
  bool _isExpatriate = false;

  bool get isExpatriate => _isExpatriate;

  set isExpatriate(bool value) {
    _isExpatriate = value;
    saveData();
    notifyListeners();
  }

  // House and car ownership
  bool _hasHouse = false;
  bool _hasCar = false;

  bool get hasHouse => _hasHouse;
  bool get hasCar => _hasCar;

  set hasHouse(bool value) {
    _hasHouse = value;
    saveData();
    notifyListeners();
  }

  set hasCar(bool value) {
    _hasCar = value;
    saveData();
    notifyListeners();
  }

  final TextEditingController professionController = TextEditingController();
  final TextEditingController monthlyIncomeController = TextEditingController();
  final TextEditingController loanPurposeController = TextEditingController();
  final TextEditingController educationController = TextEditingController();

  // Form controllers for nominee information
  final TextEditingController nomineeNameController = TextEditingController();
  final TextEditingController nomineeRelationController =
      TextEditingController();
  final TextEditingController nomineePhoneController = TextEditingController();

  // Form controllers for ID verification
  final TextEditingController nidNameController = TextEditingController();
  final TextEditingController idController = TextEditingController();

  // Form controllers for bank account
  final TextEditingController accountHolderController = TextEditingController();
  final TextEditingController bankNameController = TextEditingController();
  final TextEditingController accountNumberController = TextEditingController();
  final TextEditingController ifcCodeController = TextEditingController();

  // Image paths
  String? frontIdImagePath;
  String? backIdImagePath;
  String? selfieWithIdImagePath;
  String? signatureImagePath;

  // Image URLs from API
  String? frontIdImageUrl;
  String? backIdImageUrl;
  String? selfieWithIdImageUrl;
  String? signatureImageUrl;

  // Image bytes for web platform
  Uint8List? frontIdImageBytes;
  Uint8List? backIdImageBytes;
  Uint8List? selfieWithIdImageBytes;
  Uint8List? signatureImageBytes;

  // Verification status
  bool _isVerified = false;
  bool get isVerified => _isVerified;

  // Add setter for isVerified
  set isVerified(bool value) {
    _isVerified = value;
    notifyListeners();
  }

  // API service
  final ApiService _apiService = ApiService();

  // Current step and form completion status
  PersonalInfoStep _currentStep = PersonalInfoStep.personalInfo;
  bool _personalInfoCompleted = false;
  bool _nomineeInfoCompleted = false;
  bool _idVerificationCompleted = false;
  bool _bankAccountCompleted = false;

  // Getters
  PersonalInfoStep get currentStep => _currentStep;
  bool get personalInfoCompleted => _personalInfoCompleted;
  bool get nomineeInfoCompleted => _nomineeInfoCompleted;
  bool get idVerificationCompleted => _idVerificationCompleted;
  bool get bankAccountCompleted => _bankAccountCompleted;

  // Calculate progress (0.0 to 1.0)
  double getProgress() {
    switch (_currentStep) {
      case PersonalInfoStep.personalInfo:
        return 0.25;
      case PersonalInfoStep.nomineeInfo:
        return 0.5;
      case PersonalInfoStep.bankAccount: // Changed from idVerification
        return 0.75;
      case PersonalInfoStep.idVerification: // Changed from bankAccount
        return 1.0;
    }
  }

  // Initialize provider and load saved data
  Future<void> initialize() async {
    try {
      // First try to fetch from API
      final response = await _apiService.fetchPersonalInfo();

      // Check if API call was successful and status is 1 or 2
      if (response.success &&
          (response.data?['status'] == 1 || response.data?['status'] == 2)) {
        // Corrected status check to 1 or 2
        debugPrint("API returned status 1 or 2. Loading verified data.");
        // Fill all form controllers with API data
        _fillFormControllersFromApi(response.data!);

        // Set verified flag
        _isVerified = true;

        // Mark all steps as completed
        _personalInfoCompleted = true;
        _nomineeInfoCompleted = true;
        _idVerificationCompleted = true;
        _bankAccountCompleted = true;
        
        // Reset to first step when data is verified
        _currentStep = PersonalInfoStep.personalInfo;

        notifyListeners();
        return; // Exit after loading from API
      }

      // If API failed or user is not verified (status is not 1 or 2), load from local storage
      debugPrint(
          "API did not return status 1 or 2 or failed. Loading saved data.");
      await loadSavedData();
    } catch (e) {
      debugPrint(
          "Error during PersonalInfoProvider initialization API call: $e");
      // In case of any error during API call, still try to load from local storage
      debugPrint("Attempting to load saved data after API error.");
      await loadSavedData();
    }
    // Ensure listeners are notified even if only local data was loaded
    notifyListeners();
  }

  // Fill form controllers from API data
  void _fillFormControllersFromApi(Map<String, dynamic> data) {
    // Personal info
    nameController.text = data['name'] ?? '';
    currentAddressController.text = data['currentAddress'] ?? '';
    permanentAddressController.text = data['permanentAddress'] ?? '';
    
    // Add handling for familyMembers with null check
    familyMembers.text = data['familyMembers']?.toString() ?? '';

    // Convert numeric values from server (1/0) to boolean
    _isExpatriate = data['outsider'] == 1;
    _hasHouse = data['have_house'] == 1;
    _hasCar = data['have_car'] == 1;

    professionController.text = data['profession'] ?? '';
    monthlyIncomeController.text = data['income']?.toString() ?? '';
    loanPurposeController.text = data['loanPurpose'] ?? '';
    
    // Add handling for edu with null check
    educationController.text = data['edu'] ?? '';

    // Nominee info
    nomineeNameController.text = data['nomineeName'] ?? '';
    nomineeRelationController.text = data['nomineeRelation'] ?? '';
    nomineePhoneController.text = data['nomineePhone'] ?? '';

    // ID verification
    nidNameController.text = data['nidName'] ?? '';
    idController.text = data['nidNumber'] ?? '';

    // Bank info
    accountHolderController.text = data['bankuserName'] ?? '';
    bankNameController.text = data['bankName'] ?? '';
    accountNumberController.text = data['account'] ?? '';
    
    // Add handling for branchName with null check
    ifcCodeController.text = data['branchName'] ?? '';

    // Image URLs
    selfieWithIdImageUrl = data['selfie'];
    frontIdImageUrl = data['nidFrontImage'];
    backIdImageUrl = data['nidBackImage'];
    signatureImageUrl = data['signature'];

    // Note: Image bytes are not loaded from API, they are for local selection on web
  }

  // Set current step
  void setStep(PersonalInfoStep step) {
    _currentStep = step;
    notifyListeners();
  }

  // Move to next step
  void nextStep() {
    switch (_currentStep) {
      case PersonalInfoStep.personalInfo:
        _personalInfoCompleted = true;
        _currentStep = PersonalInfoStep.nomineeInfo;
        break;
      case PersonalInfoStep.nomineeInfo:
        _nomineeInfoCompleted = true;
        _currentStep = PersonalInfoStep.bankAccount; // Changed to bankAccount
        break;
      case PersonalInfoStep.bankAccount: // Changed from idVerification
        _bankAccountCompleted = true; // Set bankAccountCompleted
        _currentStep = PersonalInfoStep.idVerification;    // Changed to idVerification
        break;
      case PersonalInfoStep.idVerification:    // Changed from bankAccount
        _idVerificationCompleted = true; // Set idVerificationCompleted
        // Form is complete
        break;
    }

    saveData();
    notifyListeners();
  }

  // Move to previous step
  void previousStep() {
    switch (_currentStep) {
      case PersonalInfoStep.personalInfo:
        // Already at first step
        break;
      case PersonalInfoStep.nomineeInfo:
        _currentStep = PersonalInfoStep.personalInfo;
        break;
      case PersonalInfoStep.bankAccount: // Changed from idVerification
        _currentStep = PersonalInfoStep.nomineeInfo;
        break;
      case PersonalInfoStep.idVerification:    // Changed from bankAccount
        _currentStep = PersonalInfoStep.bankAccount; // Changed to bankAccount
        break;
    }

    notifyListeners();
  }

  // Save image paths
  void saveImagePath(String type, String path) {
    switch (type) {
      case 'front':
        frontIdImagePath = path;
        break;
      case 'back':
        backIdImagePath = path;
        break;
      case 'selfie':
        selfieWithIdImagePath = path;
        break;
      case 'signature':
        signatureImagePath = path;
        break;
    }

    saveData();
    notifyListeners();
  }

  // Save image bytes for web platform
  void saveImageBytes(String type, Uint8List bytes) {
    // Debug log to verify the type being set
    debugPrint(
        'saveImageBytes called with type: $type, bytes length: ${bytes.length}');

    switch (type) {
      case 'front':
        frontIdImageBytes = bytes;
        if (kIsWeb) frontIdImagePath = 'image_selected'; // Only set placeholder for web
        break;
      case 'back':
        backIdImageBytes = bytes;
        if (kIsWeb) backIdImagePath = 'image_selected'; // Only set placeholder for web
        break;
      case 'selfie':
        selfieWithIdImageBytes = bytes;
        if (kIsWeb) selfieWithIdImagePath = 'image_selected'; // Only set placeholder for web
        break;
      case 'signature':
        signatureImageBytes = bytes;
        if (kIsWeb) signatureImagePath = 'image_selected'; // Only set placeholder for web
        break;
      default:
        debugPrint('Warning: Unknown image type: $type');
        break;
    }

    saveData();
    notifyListeners();
  }

  // Method to clear Front ID Image
  void clearFrontIdImage() {
    frontIdImagePath = null;
    frontIdImageBytes = null;
    saveData(); // Ensure data is saved after clearing
    notifyListeners();
  }

  // Method to clear Back ID Image
  void clearBackIdImage() {
    backIdImagePath = null;
    backIdImageBytes = null;
    saveData(); // Ensure data is saved after clearing
    notifyListeners();
  }

  // Method to clear Selfie ID Image
  void clearSelfieIdImage() {
    selfieWithIdImagePath = null;
    selfieWithIdImageBytes = null;
    saveData(); // Ensure data is saved after clearing
    notifyListeners();
  }

  // Clear all data
  void clearData() {
    debugPrint("PersonalInfoProvider.clearData() called");

    // Save the current verification status
    bool wasVerified = _isVerified;

    // Clear form controllers
    nameController.clear();
    currentAddressController.clear();
    permanentAddressController.clear(); // Clear permanent address

    // Reset house and car ownership
    _hasHouse = false;
    _hasCar = false;

    professionController.clear();
    monthlyIncomeController.clear();
    loanPurposeController.clear();
    educationController.clear();

    nomineeNameController.clear();
    nomineeRelationController.clear();
    nomineePhoneController.clear();

    nidNameController.clear();
    idController.clear();

    accountHolderController.clear();
    bankNameController.clear();
    accountNumberController.clear();
    ifcCodeController.clear();

    // Clear image paths
    frontIdImagePath = null;
    backIdImagePath = null;
    selfieWithIdImagePath = null;
    signatureImagePath = null;

    // Reset step and completion status
    _currentStep = PersonalInfoStep.personalInfo;
    _personalInfoCompleted = false;
    _nomineeInfoCompleted = false;
    _idVerificationCompleted = false;
    _bankAccountCompleted = false;

    // Restore the verification status
    _isVerified = wasVerified;

    // Clear saved data
    clearSavedData().then((_) {
      debugPrint("SharedPreferences data has been cleared successfully");
    }).catchError((error) {
      debugPrint("Error while clearing SharedPreferences: $error");
    });

    notifyListeners();
  }

  // Save data to SharedPreferences
  Future<void> saveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save form data
      await prefs.setString('pi_name', nameController.text);
      await prefs.setString('pi_currentAddress', currentAddressController.text);
      await prefs.setString('pi_permanentAddress',
          permanentAddressController.text); // Save permanent address
      // Save expatriate status
      await prefs.setBool('pi_isExpatriate', _isExpatriate);
      // Save house and car ownership
      await prefs.setBool('pi_hasHouse', _hasHouse);
      await prefs.setBool('pi_hasCar', _hasCar);

      await prefs.setString('pi_profession', professionController.text);
      await prefs.setString('pi_monthlyIncome', monthlyIncomeController.text);
      await prefs.setString('pi_loanPurpose', loanPurposeController.text);
      await prefs.setString('pi_education', educationController.text);

      await prefs.setString('pi_nomineeName', nomineeNameController.text);
      await prefs.setString(
          'pi_nomineeRelation', nomineeRelationController.text);
      await prefs.setString('pi_nomineePhone', nomineePhoneController.text);

      await prefs.setString('pi_nidName', nidNameController.text);
      await prefs.setString('pi_id', idController.text);

      await prefs.setString('pi_accountHolder', accountHolderController.text);
      await prefs.setString('pi_bankName', bankNameController.text);
      await prefs.setString('pi_accountNumber', accountNumberController.text);
      await prefs.setString('pi_ifcCode', ifcCodeController.text);

      // Save image paths
      if (frontIdImagePath != null) {
        await prefs.setString('pi_frontIdImagePath', frontIdImagePath!);
      }
      if (backIdImagePath != null) {
        await prefs.setString('pi_backIdImagePath', backIdImagePath!);
      }
      if (selfieWithIdImagePath != null) {
        await prefs.setString(
            'pi_selfieWithIdImagePath', selfieWithIdImagePath!);
      }
      if (signatureImagePath != null) {
        await prefs.setString('pi_signatureImagePath', signatureImagePath!);
      }

      // Save step and completion status
      await prefs.setInt('pi_currentStep', _currentStep.index);
      await prefs.setBool('pi_personalInfoCompleted', _personalInfoCompleted);
      await prefs.setBool('pi_nomineeInfoCompleted', _nomineeInfoCompleted);
      await prefs.setBool(
          'pi_idVerificationCompleted', _idVerificationCompleted);
      await prefs.setBool('pi_bankAccountCompleted', _bankAccountCompleted);
    } catch (e) {
      debugPrint("Error saving personal info data: $e");
      // Continue even if data couldn't be saved
    }
  }

  // Load data from SharedPreferences
  Future<void> loadSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load expatriate status
      _isExpatriate = prefs.getBool('pi_isExpatriate') ?? false;
      _hasHouse = prefs.getBool('pi_hasHouse') ?? false;
      _hasCar = prefs.getBool('pi_hasCar') ?? false;

      // Load form data
      nameController.text = prefs.getString('pi_name') ?? '';
      currentAddressController.text =
          prefs.getString('pi_currentAddress') ?? '';
      permanentAddressController.text =
          prefs.getString('pi_permanentAddress') ??
              ''; // Load permanent address

      professionController.text = prefs.getString('pi_profession') ?? '';
      monthlyIncomeController.text = prefs.getString('pi_monthlyIncome') ?? '';
      loanPurposeController.text = prefs.getString('pi_loanPurpose') ?? '';
      educationController.text = prefs.getString('pi_education') ?? '';

      nomineeNameController.text = prefs.getString('pi_nomineeName') ?? '';
      nomineeRelationController.text =
          prefs.getString('pi_nomineeRelation') ?? '';
      nomineePhoneController.text = prefs.getString('pi_nomineePhone') ?? '';

      nidNameController.text = prefs.getString('pi_nidName') ?? '';
      idController.text = prefs.getString('pi_id') ?? '';

      accountHolderController.text = prefs.getString('pi_accountHolder') ?? '';
      bankNameController.text = prefs.getString('pi_bankName') ?? '';
      accountNumberController.text = prefs.getString('pi_accountNumber') ?? '';
      ifcCodeController.text = prefs.getString('pi_ifcCode') ?? '';

      // Load image paths
      frontIdImagePath = prefs.getString('pi_frontIdImagePath');
      backIdImagePath = prefs.getString('pi_backIdImagePath');
      selfieWithIdImagePath = prefs.getString('pi_selfieWithIdImagePath');
      signatureImagePath = prefs.getString('pi_signatureImagePath');

      // Load step and completion status
      final stepIndex = prefs.getInt('pi_currentStep');
      if (stepIndex != null &&
          stepIndex >= 0 &&
          stepIndex < PersonalInfoStep.values.length) {
        _currentStep = PersonalInfoStep.values[stepIndex];
      }

      _personalInfoCompleted =
          prefs.getBool('pi_personalInfoCompleted') ?? false;
      _nomineeInfoCompleted = prefs.getBool('pi_nomineeInfoCompleted') ?? false;
      _idVerificationCompleted =
          prefs.getBool('pi_idVerificationCompleted') ?? false;
      _bankAccountCompleted = prefs.getBool('pi_bankAccountCompleted') ?? false;

      notifyListeners();
    } catch (e) {
      debugPrint("Error loading personal info data: $e");
      // Reset to default state in case of error
      _currentStep = PersonalInfoStep.personalInfo;
      _personalInfoCompleted = false;
      _nomineeInfoCompleted = false;
      _idVerificationCompleted = false;
      _bankAccountCompleted = false;
    }
  }

  // Clear saved data
  Future<void> clearSavedData() async {
    try {
      debugPrint("PersonalInfoProvider.clearSavedData() called");
      final prefs = await SharedPreferences.getInstance();

      // Clear all personal info related keys
      final keys =
          prefs.getKeys().where((key) => key.startsWith('pi_')).toList();

      debugPrint("Found ${keys.length} personal info keys to remove: $keys");

      for (var key in keys) {
        await prefs.remove(key);
        debugPrint("Removed key: $key");
      }

      // Verify keys were removed
      final remainingKeys =
          prefs.getKeys().where((key) => key.startsWith('pi_')).toList();
      debugPrint("Remaining personal info keys after removal: $remainingKeys");
    } catch (e) {
      debugPrint("Error clearing personal info data: $e");
      // Continue even if data couldn't be cleared
    }
  }

  // Dispose resources
  @override
  void dispose() {
    // Dispose all TextEditingControllers
    nameController.dispose();
    currentAddressController.dispose();
    permanentAddressController
        .dispose(); // Dispose permanent address controller

    professionController.dispose();
    monthlyIncomeController.dispose();
    loanPurposeController.dispose();
    educationController.dispose();

    nomineeNameController.dispose();
    nomineeRelationController.dispose();
    nomineePhoneController.dispose();

    nidNameController.dispose();
    idController.dispose();

    accountHolderController.dispose();
    bankNameController.dispose();
    accountNumberController.dispose();
    ifcCodeController.dispose();

    super.dispose();
  }

  // Check if signature exists
  bool get hasSignature {
    if (kIsWeb) {
      // For web, a signature exists if bytes are present OR an API URL is available
      return (signatureImageBytes != null && signatureImageBytes!.isNotEmpty) ||
             (signatureImageUrl != null && signatureImageUrl!.isNotEmpty);
    } else {
      // For mobile, a signature exists if an API URL is available,
      // OR local bytes are present,
      // OR a valid (non-placeholder) local path exists.
      return (signatureImageUrl != null && signatureImageUrl!.isNotEmpty) ||
             (signatureImageBytes != null && signatureImageBytes!.isNotEmpty) ||
             (signatureImagePath != null && signatureImagePath!.isNotEmpty && signatureImagePath != 'image_selected');
    }
  }

  // Clear signature
  void clearSignature() {
    signatureImagePath = null;
    signatureImageBytes = null;
    notifyListeners();
  }

  bool validatePersonalInfo() {
    return nameController.text.isNotEmpty &&
        currentAddressController.text.isNotEmpty &&
        permanentAddressController.text.isNotEmpty &&
        professionController.text.isNotEmpty &&
        monthlyIncomeController.text.isNotEmpty &&
        loanPurposeController.text.isNotEmpty &&
        educationController.text.isNotEmpty;
  }

  bool validateNomineeInfo() {
    return nomineeNameController.text.isNotEmpty &&
        nomineeRelationController.text.isNotEmpty &&
        nomineePhoneController.text.isNotEmpty;
  }

  bool validateIdVerification() {
    return nidNameController.text.isNotEmpty &&
        idController.text.isNotEmpty &&
        frontIdImagePath != null &&
        frontIdImagePath!.isNotEmpty &&
        backIdImagePath != null &&
        backIdImagePath!.isNotEmpty &&
        selfieWithIdImagePath != null &&
        selfieWithIdImagePath!.isNotEmpty;
  }

  bool validateBankAccount() {
    return accountHolderController.text.isNotEmpty &&
        bankNameController.text.isNotEmpty &&
        accountNumberController.text.isNotEmpty &&
        ifcCodeController.text.isNotEmpty;
  }
}
