{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ai_apps\\loan_app_flutter\\android\\app\\.cxx\\RelWithDebInfo\\192j516p\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ai_apps\\loan_app_flutter\\android\\app\\.cxx\\RelWithDebInfo\\192j516p\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}