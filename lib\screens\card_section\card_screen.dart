import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:world_bank_loan/screens/home_section/withdraw/uttolon_screen.dart';
import 'package:world_bank_loan/screens/home_section/withdraw/withdraw_screen.dart';
import '../../providers/card_provider.dart';
import '../../core/theme/app_theme.dart';

// Constant widgets
const height5 = SizedBox(height: 5);
const height10 = SizedBox(height: 10);
const height15 = SizedBox(height: 15);
const height16 = SizedBox(height: 16);
const width16 = SizedBox(width: 16);

// Constant styles
const balanceTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 16,
  fontWeight: FontWeight.w500,
);

const detailTitleStyle = TextStyle(
  color: Colors.black,
  fontSize: 18,
  fontWeight: FontWeight.bold,
);

class CardScreen extends StatefulWidget {
  const CardScreen({super.key});

  @override
  _CardScreenState createState() => _CardScreenState();
}

class _CardScreenState extends State<CardScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isDisposed = false;
  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 500), // Reduced duration
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.fastOutSlowIn, // More efficient curve
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: Offset(0, 0.1), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(0.2, 1.0, curve: Curves.easeOut),
      ),
    );

    // Set status bar icons to white
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isDisposed) {
        _initializeCardData();
      }
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initializeCardData() async {
    if (!mounted || _isDisposed) return;

    final provider = Provider.of<CardProvider>(context, listen: false);
    try {
      await provider.fetchCardData();
      // Check if still mounted after async operation
      if (mounted && !_isDisposed) {
        _animationController.forward();
      }
    } catch (e) {
      print('Error initializing card data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isDesktop = screenWidth >= 900;
    return Consumer<CardProvider>(
      builder: (context, provider, _) {
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: AppTheme.authorityBlue,
            centerTitle: true,
            title: Center(
              child: ConstrainedBox(
                constraints:
                    BoxConstraints(maxWidth: isDesktop ? 600 : double.infinity),
                child: Text(
                  'আমার ব্যাংকিং',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            actions: [
              if (provider.status == CardLoadingStatus.loaded)
                IconButton(
                  icon: Icon(Icons.refresh, color: Colors.white),
                  onPressed: () {
                    if (!mounted || _isDisposed) return;
                    provider.fetchCardData();
                    if (!_isDisposed) {
                      _animationController.reset();
                      _animationController.forward();
                    }
                  },
                ),
            ],
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.light,
              statusBarBrightness: Brightness.dark,
            ),
          ),
          body: Stack(
            children: [
              // Gradient background
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppTheme.authorityBlue,
                      AppTheme.trustCyan,
                      AppTheme.backgroundLight,
                    ],
                    stops: [0.0, 0.2, 0.4],
                  ),
                ),
              ),
              SafeArea(
                child: isDesktop
                    ? Center(
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 600),
                          child: AnimatedBuilder(
                            animation: _animationController,
                            builder: (context, child) {
                              return FadeTransition(
                                opacity: _fadeAnimation,
                                child: SlideTransition(
                                  position: _slideAnimation,
                                  child: _buildBody(provider, context),
                                ),
                              );
                            },
                          ),
                        ),
                      )
                    : AnimatedBuilder(
                        animation: _animationController,
                        builder: (context, child) {
                          return FadeTransition(
                            opacity: _fadeAnimation,
                            child: SlideTransition(
                              position: _slideAnimation,
                              child: _buildBody(provider, context),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBody(CardProvider provider, BuildContext context) {
    switch (provider.status) {
      case CardLoadingStatus.loading:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
              SizedBox(height: 16),
              Text(
                "আপনার কার্ডের বিবরণ লোড হচ্ছে...",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );

      case CardLoadingStatus.error:
        return Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 24),
            padding: EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 15,
                  offset: Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 50,
                    color: Colors.red[400],
                  ),
                ),
                SizedBox(height: 24),
                Text(
                  "কার্ডের বিবরণ লোড করা যায়নি",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.neutral800,
                  ),
                ),
                SizedBox(height: 12),
                Text(
                  provider.errorMessage ?? "একটি অজানা ত্রুটি ঘটেছে",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppTheme.neutral600,
                    fontSize: 14,
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    if (!mounted || _isDisposed) return;
                    provider.fetchCardData();
                    if (!_isDisposed) {
                      _animationController.reset();
                      _animationController.forward();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.authorityBlue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 36, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    'আবার চেষ্টা করুন',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );

      case CardLoadingStatus.loaded:
        return SingleChildScrollView(
          padding: EdgeInsets.only(bottom: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(child: _buildCreditCard(provider, context)),
              SizedBox(height: 1),
              _buildBalanceSection(provider),
              //SizedBox(height: 10),
              // Move buttons here
              _rechargeAndWithdrawMethod(context),
              SizedBox(height: 15),
              // Withdraw list section after buttons
              _buildWithdrawSection(provider),
            ],
          ),
        );

      default: // Initial state or any other state
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
              SizedBox(height: 16),
              Text(
                "আরম্ভ করা হচ্ছে...",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
    }
  }

  Widget _rechargeAndWithdrawMethod(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WithdrawScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.authorityBlue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'রিচার্জ',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => UttolonScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.authorityBlue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'উত্তোলন',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditCard(CardProvider provider, BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isDesktop = screenWidth >= 900;
    final double containerWidth = isDesktop ? 600 : screenWidth;
    // Reduced padding even further for wider card
    final double cardWidth =
        containerWidth - (isDesktop ? 20 : 32); // Reduced padding values
    final double cardHeight = cardWidth * 0.63;

    return Container(
        width: containerWidth,
        margin: EdgeInsets.symmetric(vertical: 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateX(0.05),
                child: Container(
                  width: cardWidth, // This will now be wider
                  height: cardHeight,
                  // Rest of the code remains unchanged
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: Offset(0, 15),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Stack(
                      children: [
                        // Card background with gradient
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.authorityBlue.withOpacity(0.9),
                                AppTheme.trustCyan,
                                Colors.cyan.shade300,
                              ],
                            ),
                          ),
                        ),
                        // Background pattern
                        Opacity(
                          opacity: 0.1,
                          child: CustomPaint(
                            size: Size(cardWidth, cardHeight),
                            painter: CardPatternPainter(),
                          ),
                        ),
                        // Card content
                        Padding(
                          padding: EdgeInsets.all(isDesktop ? 32 : 22),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  // Chip
                                  Container(
                                    height: isDesktop ? 50 : 40,
                                    width: isDesktop ? 65 : 65,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.amber.shade300,
                                          Colors.amber.shade400,
                                          Colors.amber.shade500,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(10),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          blurRadius: 4,
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Center(
                                      child: Icon(
                                        Icons.credit_score,
                                        color: Colors.amber.shade800,
                                        size: isDesktop ? 30 : 24,
                                      ),
                                    ),
                                  ),
                                  // VISA logo
                                  Text(
                                    'VISA',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: isDesktop ? 32 : 26,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 1,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black26,
                                          blurRadius: 2,
                                          offset: Offset(1, 1),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              Spacer(),
                              Text(
                                provider.userBankNumber.isNotEmpty
                                    ? _formatCardNumber(provider.userBankNumber)
                                    : '**** **** **** ****',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: isDesktop ? 28 : 22,
                                  letterSpacing: 2,
                                  fontWeight: FontWeight.w500,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black26,
                                      blurRadius: 2,
                                      offset: Offset(1, 1),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: isDesktop ? 30 : 20),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'BANK NAME',
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.7),
                                          fontSize: isDesktop ? 14 : 11,
                                          letterSpacing: 1,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(
                                        provider.userBankName.isNotEmpty
                                            ? provider.userBankName
                                                .toUpperCase()
                                            : 'BANK NAME',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: isDesktop ? 20 : 16,
                                          fontWeight: FontWeight.w500,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black26,
                                              blurRadius: 1,
                                              offset: Offset(0.5, 0.5),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'VALID THRU',
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.7),
                                          fontSize: isDesktop ? 14 : 11,
                                          letterSpacing: 1,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(
                                        provider.validity.isNotEmpty
                                            ? provider.validity
                                            : 'MM/YY',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: isDesktop ? 20 : 16,
                                          fontWeight: FontWeight.w500,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black26,
                                              blurRadius: 1,
                                              offset: Offset(0.5, 0.5),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  String _formatCardNumber(String number) {
    // Format the card number in groups of 4 digits
    if (number.length != 16) return number;
    return '${number.substring(0, 4)} ${number.substring(4, 8)} ${number.substring(8, 12)} ${number.substring(12, 16)}';
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: AppTheme.neutral800,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildDetailItem(
      String label, String value, IconData icon, Color color) {
    return Container(
        margin: EdgeInsets.only(bottom: 12),
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: AppTheme.neutral200,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: 22,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      color: AppTheme.neutral600,
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    value,
                    style: TextStyle(
                      color: AppTheme.neutral800,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  Widget _buildFeatureItem(
      String title, String description, IconData icon, Color color) {
    return Container(
        margin: EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: AppTheme.neutral200,
            width: 1,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              // Handle tap action
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Coming soon: $title'),
                  backgroundColor: color,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  margin: EdgeInsets.all(16),
                ),
              );
            },
            splashColor: color.withOpacity(0.1),
            highlightColor: color.withOpacity(0.05),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, size: 22, color: color),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            color: AppTheme.neutral800,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          description,
                          style: TextStyle(
                            color: AppTheme.neutral600,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: AppTheme.neutral400,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ));
  }

// balance section-------------------------------------------------
  Widget _buildBalanceSection(CardProvider provider) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isMobile = screenWidth < 600;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      width: isMobile ? double.infinity : 600,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppTheme.authorityBlue,
            AppTheme.trustCyan,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _BalanceColumn(
              label: "ব্যালেন্স (টাকা)", amount: provider.walletBalance),
          _BalanceColumn(label: "লোন (টাকা)", amount: provider.walletLoan),
        ],
      ),
    );
  }

  Widget _buildWithdrawSection(CardProvider provider) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isMobile = screenWidth < 600;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18),
      width: isMobile ? double.infinity : 600,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("বিস্তারিত", style: detailTitleStyle),
          height10,
          Container(
            constraints: BoxConstraints(
              maxWidth: screenWidth > 600 ? 600 : screenWidth,
              maxHeight: 300,
            ),
            child: RefreshIndicator(
              onRefresh: () {
                return provider.refreshWithdraws();
              },
              child: provider.withdrawList.isEmpty
                  ? ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        Container(
                          height: 300,
                          child: _buildEmptyState(),
                        ),
                      ],
                    )
                  : _buildWithdrawList(provider),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 48, color: Colors.grey[400]),
          SizedBox(height: 16),
          Text(
            "কোনো উত্তোলন তথ্য পাওয়া যায়নি",
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWithdrawList(CardProvider provider) {
    final scrollController = ScrollController();

    // Add scroll listener for pagination
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 200 &&
          !provider.isLoadingMore &&
          provider.hasMoreItems) {
        provider.loadMoreWithdraws();
      }
    });

    return ListView.builder(
      controller: scrollController,
      physics: AlwaysScrollableScrollPhysics(),
      itemCount: provider.withdrawList.length + (provider.hasMoreItems ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= provider.withdrawList.length) {
          return _buildLoadingIndicator();
        }

        var withdraw = provider.withdrawList[index];
        return Card(
          margin: EdgeInsets.symmetric(vertical: 4),
          elevation: 2,
          child: ListTile(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "টাকা: ${withdraw['amount']}",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                _buildStatusChip(withdraw['status']),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${withdraw['created_at']}",
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                if (withdraw['status'] == 2 && withdraw['reason'] != null)
                  Padding(
                    padding: EdgeInsets.only(top: 4),
                    child: Text(
                      "${withdraw['reason']}",
                      style: TextStyle(color: Colors.red[700], fontSize: 13),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(int status) {
    String text;
    Color color;
    Color textColor = Colors.white;

    switch (status) {
      case 0:
        text = 'Pending';
        color = Colors.orange;
        break;
      case 1:
        text = 'Accepted';
        color = Colors.green;
        break;
      case 2:
        text = 'Rejected';
        color = Colors.red;
        break;
      default:
        text = 'Unknown';
        color = Colors.grey;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),
      alignment: Alignment.center,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.authorityBlue),
      ),
    );
  }
}

class _BalanceColumn extends StatelessWidget {
  const _BalanceColumn({
    Key? key,
    required this.label,
    required this.amount,
  }) : super(key: key);

  final String label;
  final String amount;

  static const _labelStyle = TextStyle(
    color: Colors.white,
    fontSize: 16,
  );

  static const _amountStyle = TextStyle(
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(label, style: _labelStyle),
        height5,
        Text(amount, style: _amountStyle),
      ],
    );
  }
}

class CardPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(0, size.height * 0.2)
      ..quadraticBezierTo(
          size.width * 0.5, size.height * 0.4, size.width, size.height * 0.2)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
